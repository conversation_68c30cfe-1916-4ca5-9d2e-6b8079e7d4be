<\!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FaceTrace - Advanced Facial Recognition Search</title>
    <meta name="description" content="FaceTrace - Find where faces appear across the web with advanced facial recognition technology">
    <link rel="icon" href="/favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        .background-pattern {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 50%, rgba(0, 255, 157, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 157, 255, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 40% 20%, rgba(157, 0, 255, 0.15) 0%, transparent 50%);
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            padding: 2rem;
            text-align: center;
            z-index: 1;
        }

        .logo-container {
            margin-bottom: 3rem;
            animation: fadeInUp 0.8s ease-out;
        }

        .logo {
            width: 180px;
            height: auto;
            filter: drop-shadow(0 4px 20px rgba(0, 255, 157, 0.3));
        }

        h1 {
            font-size: 3.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #00ff9d 0%, #009dff 50%, #9d00ff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .subtitle {
            font-size: 1.25rem;
            color: #a1a1aa;
            margin-bottom: 3rem;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 2rem;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(0, 255, 157, 0.3);
            box-shadow: 0 10px 30px rgba(0, 255, 157, 0.2);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .feature-title {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #00ff9d;
        }

        .feature-description {
            color: #a1a1aa;
            line-height: 1.6;
        }

        .cta-button {
            display: inline-block;
            padding: 1rem 3rem;
            background: linear-gradient(135deg, #00ff9d 0%, #009dff 100%);
            color: #000000;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            animation: fadeInUp 0.8s ease-out 0.8s both;
            box-shadow: 0 4px 20px rgba(0, 255, 157, 0.3);
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 255, 157, 0.5);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .pulse-glow {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 255, 157, 0.4);
            }
            70% {
                box-shadow: 0 0 0 20px rgba(0, 255, 157, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(0, 255, 157, 0);
            }
        }

        @media (max-width: 768px) {
            h1 {
                font-size: 2.5rem;
            }
            .subtitle {
                font-size: 1rem;
            }
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="background-pattern"></div>
    
    <div class="container">
        <div class="logo-container">
            <svg class="logo" viewBox="0 0 200 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                <text x="10" y="45" font-family="Arial, sans-serif" font-size="36" font-weight="bold">
                    <tspan fill="#00ff9d">Face</tspan><tspan fill="#009dff">Trace</tspan>
                </text>
            </svg>
        </div>

        <h1>FaceTrace</h1>
        <p class="subtitle">Advanced Facial Recognition Search Technology</p>

        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🔍</div>
                <h3 class="feature-title">Powerful Search</h3>
                <p class="feature-description">
                    Upload any photo and discover where faces appear across the web with our advanced AI-powered facial recognition technology.
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🛡️</div>
                <h3 class="feature-title">Privacy First</h3>
                <p class="feature-description">
                    Your searches are secure and confidential. We prioritize user privacy with encrypted uploads and secure processing.
                </p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">Lightning Fast</h3>
                <p class="feature-description">
                    Get results in seconds with our optimized search algorithms and powerful infrastructure designed for speed.
                </p>
            </div>
        </div>

        <a href="/search" class="cta-button pulse-glow">Start Your Search</a>
    </div>
</body>
</html>
EOF < /dev/null