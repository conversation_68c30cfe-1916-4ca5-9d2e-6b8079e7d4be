# Retrieve Google Cloud API Key using gcloud CLI

## 🔧 Prerequisites

First, make sure you have gcloud CLI installed and authenticated:

```bash
# Install gcloud CLI (if not already installed)
# macOS:
brew install google-cloud-sdk

# Or download from: https://cloud.google.com/sdk/docs/install

# Authenticate with your Google account
gcloud auth login

# Set your project
gcloud config set project gold-braid-458901-v2
```

## 🔑 Commands to Retrieve API Key

### 1. List All API Keys
```bash
gcloud services api-keys list --project=gold-braid-458901-v2
```

### 2. Get Specific API Key Details
```bash
# Replace KEY_ID with the actual key ID from the list command
gcloud services api-keys describe KEY_ID --project=gold-braid-458901-v2
```

### 3. Get API Key String (if you have the key ID)
```bash
# This will show the actual API key string
gcloud services api-keys get-key-string KEY_ID --project=gold-braid-458901-v2
```

### 4. Create a New API Key (if needed)
```bash
# Create a new API key for reCAPTCHA Enterprise
gcloud services api-keys create \
    --display-name="reCAPTCHA Enterprise API Key" \
    --project=gold-braid-458901-v2
```

### 5. Restrict API Key to reCAPTCHA Enterprise (recommended)
```bash
# Replace KEY_ID with your actual key ID
gcloud services api-keys update KEY_ID \
    --api-target=service=recaptchaenterprise.googleapis.com \
    --project=gold-braid-458901-v2
```

## 🌐 Alternative: Get from Google Cloud Console

If you prefer using the web interface:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select project: `gold-braid-458901-v2`
3. Navigate to **APIs & Services** > **Credentials**
4. Look for **API Keys** section
5. Click on your API key to view/copy it

## 🔍 Current Configuration Check

Your current API key in `.env` is:
```
GOOGLE_CLOUD_API_KEY="AIzaSyCzQcHaY9eOpYIlHF2tOLJ6y-3i81Lt7-c"
```

To verify this key is valid, run:
```bash
# Test the API key with a simple request
curl -X GET \
  "https://recaptchaenterprise.googleapis.com/v1/projects/gold-braid-458901-v2?key=AIzaSyCzQcHaY9eOpYIlHF2tOLJ6y-3i81Lt7-c"
```

## 🛠️ Troubleshooting

### If gcloud is not authenticated:
```bash
gcloud auth login
gcloud auth application-default login
```

### If you get permission errors:
```bash
# Make sure you have the right permissions
gcloud projects get-iam-policy gold-braid-458901-v2
```

### If API key doesn't work:
```bash
# Enable the reCAPTCHA Enterprise API
gcloud services enable recaptchaenterprise.googleapis.com --project=gold-braid-458901-v2
```

## 📋 Quick Commands Summary

```bash
# 1. Set project
gcloud config set project gold-braid-458901-v2

# 2. List API keys
gcloud services api-keys list

# 3. Get key details (replace KEY_ID)
gcloud services api-keys describe KEY_ID

# 4. Get actual key string (replace KEY_ID)
gcloud services api-keys get-key-string KEY_ID
```

## ✅ Verification

Once you have your API key, test it with your reCAPTCHA setup:
1. Update `.env` with the correct key
2. Visit `http://localhost:3001/captcha-test`
3. Check the verification results

Your current setup should already be working with the API key you have configured!
