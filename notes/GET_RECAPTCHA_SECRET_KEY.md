# How to Get Your reCAPTCHA Enterprise Secret Key

## 🔑 Current Issue
You have a Google OAuth client secret (`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`) instead of the reCAPTCHA secret key.

## 📋 Steps to Get the Correct Secret Key

### Method 1: reCAPTCHA Enterprise Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select project: `gold-braid-458901-v2`
3. Navigate to **Security** > **reCAPTCHA Enterprise**
4. Find your site key: `6Lc6l1UrAAAAADAs8XyJsTjfsaT57xIRrIiKY7ls`
5. Click on the key name to open details
6. Go to the **Integration** tab
7. Look for **"USE LEGACY KEY"** text at the top
8. Click it to reveal the secret key
9. Copy the secret key (it should start with `6L` and be different from your site key)

### Method 2: Legacy reCAPTCHA Console
1. Go to [reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin)
2. Find your site key: `6Lc6l1UrAAAAADAs8XyJsTjfsaT57xIRrIiKY7ls`
3. Click on the gear icon (Settings)
4. Copy the **Secret Key** from the settings page

## 🔧 Update Your Configuration

Once you have the correct secret key, update your `.env` file:

```bash
# Replace the OAuth client secret with the actual reCAPTCHA secret key
RECAPTCHA_SECRET_KEY="6L[your_actual_secret_key_here]"
```

## 🧪 Current Workaround

I've temporarily enabled bypass mode for development:
```bash
BYPASS_CAPTCHA_IN_DEV="true"
```

This means:
- ✅ Your application will work in development mode
- ✅ CAPTCHA verification is bypassed for testing
- ✅ All API endpoints will accept requests
- ⚠️ You still need the correct secret key for production

## 🚀 Test Your Setup

1. Visit: `http://localhost:3000/captcha-test`
2. Try the CAPTCHA verification
3. It should work now with bypass mode enabled
4. Check the logs for verification success

## 📝 What Each Key Does

### Site Key (Public)
- **Current**: `6Lc6l1UrAAAAADAs8XyJsTjfsaT57xIRrIiKY7ls`
- **Purpose**: Used in frontend JavaScript
- **Format**: Starts with `6L` followed by alphanumeric characters
- **Visibility**: Public, included in HTML

### Secret Key (Private)
- **Current**: `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` ❌ (This is OAuth, not reCAPTCHA)
- **Needed**: `6L[secret_key_here]` ✅
- **Purpose**: Used for server-side verification
- **Format**: Starts with `6L` followed by different alphanumeric characters
- **Visibility**: Private, server-side only

### API Key (Google Cloud)
- **Current**: `AIzaSyCzQcHaY9eOpYIlHF2tOLJ6y-3i81Lt7-c` ✅
- **Purpose**: Authenticates with Google Cloud APIs
- **Format**: Starts with `AIza`
- **Status**: Correctly configured

## 🔍 Quick Check

Your reCAPTCHA secret key should:
- ✅ Start with `6L`
- ✅ Be different from your site key
- ✅ Be about 40 characters long
- ❌ NOT start with `GOCSPX-` (that's OAuth)
- ❌ NOT start with `AIza` (that's API key)

## 🎯 Next Steps

1. **Get the correct secret key** using the steps above
2. **Update `.env`** with the correct secret key
3. **Set `BYPASS_CAPTCHA_IN_DEV="false"`** to test real verification
4. **Test on production** domain for full Enterprise features

Your application is working now with bypass mode, but you'll need the correct secret key for production deployment!
