# reCAPTCHA Enterprise Setup Guide

## ✅ Current Configuration

Your reCAPTCHA Enterprise integration has been updated with the new site key:

### 🔑 Site Key Configuration
- **New Site Key**: `6Lc6l1UrAAAAADAs8XyJsTjfsaT57xIRrIiKY7ls`
- **Project ID**: `gold-braid-458901-v2`
- **API Endpoint**: `https://recaptchaenterprise.googleapis.com/v1/projects/gold-braid-458901-v2/assessments?key=API_KEY`

### 📋 Required Actions

#### 1. Get Your Google Cloud API Key
You need to obtain the API key for the reCAPTCHA Enterprise API:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select project `gold-braid-458901-v2`
3. Navigate to **APIs & Services** > **Credentials**
4. Create or find your API key for reCAPTCHA Enterprise
5. Update the `.env` file:

```bash
# Replace YOUR_GOOGLE_CLOUD_API_KEY_HERE with your actual API key
GOOGLE_CLOUD_API_KEY="your_actual_api_key_here"
```

#### 2. Configure Domain Settings
In the Google reCAPTCHA Enterprise console:

1. Go to [reCAPTCHA Enterprise Console](https://console.cloud.google.com/security/recaptcha)
2. Select your site key `6Lc6l1UrAAAAADAs8XyJsTjfsaT57xIRrIiKY7ls`
3. Add these domains:
   - `facetrace.pro` (production)
   - `localhost` (development)
   - `127.0.0.1` (development)

## 🔧 Implementation Details

### Frontend Integration
The frontend now uses the proper Enterprise API pattern:

```javascript
grecaptcha.enterprise.ready(async () => {
  const token = await grecaptcha.enterprise.execute('6Lc6l1UrAAAAADAs8XyJsTjfsaT57xIRrIiKY7ls', {
    action: 'LOGIN'
  });
});
```

### Backend Verification
The backend sends requests to:
```
POST https://recaptchaenterprise.googleapis.com/v1/projects/gold-braid-458901-v2/assessments?key=API_KEY
```

With payload:
```json
{
  "event": {
    "token": "TOKEN",
    "expectedAction": "USER_ACTION",
    "siteKey": "6Lc6l1UrAAAAADAs8XyJsTjfsaT57xIRrIiKY7ls"
  }
}
```

## 🧪 Testing

### Current Status
- ✅ **Frontend**: Generates tokens with new site key
- ✅ **Backend**: Handles Enterprise API format
- ✅ **Development**: Graceful handling of domain mismatches
- ⚠️ **API Key**: Needs to be configured

### Test the Integration
1. Visit: `http://localhost:3000/captcha-test`
2. Click "Verify Security" to test frontend token generation
3. Check the backend verification results
4. Use "Test Backend with Bypass Token" for backend-only testing

## 🚀 Production Deployment

### Environment Variables
Ensure these are set in production:

```bash
# reCAPTCHA Enterprise Configuration
NEXT_PUBLIC_RECAPTCHA_SITE_KEY="6Lc6l1UrAAAAADAs8XyJsTjfsaT57xIRrIiKY7ls"
RECAPTCHA_SECRET_KEY="your_secret_key_here"
GOOGLE_CLOUD_PROJECT_ID="gold-braid-458901-v2"
NEXT_PUBLIC_GOOGLE_CLOUD_PROJECT_ID="gold-braid-458901-v2"
GOOGLE_CLOUD_API_KEY="your_api_key_here"
FORCE_STANDARD_RECAPTCHA="false"
NEXT_PUBLIC_FORCE_STANDARD_RECAPTCHA="false"
```

### Security Features
- ✅ **Action-based verification**: Different actions for different use cases
- ✅ **Risk score analysis**: Configurable thresholds (default: 0.5)
- ✅ **IP validation**: Client IP extraction and verification
- ✅ **Development mode**: Lenient handling for testing
- ✅ **Fallback support**: Graceful fallback to standard reCAPTCHA

## 📊 Monitoring

### Google Cloud Console
Monitor your reCAPTCHA Enterprise usage:
1. Go to [reCAPTCHA Enterprise Console](https://console.cloud.google.com/security/recaptcha)
2. View analytics, risk scores, and usage statistics
3. Adjust risk thresholds based on your needs

### Application Logs
The application provides detailed logging:
- Token generation success/failure
- Backend verification results
- Risk scores and actions
- Error handling and fallbacks

## 🔍 Troubleshooting

### Common Issues
1. **"browser-error"**: Domain not configured in reCAPTCHA console
2. **"invalid-input-secret"**: Wrong secret key or API key
3. **"hostname-mismatch"**: Domain restrictions in reCAPTCHA settings

### Development Mode
The system automatically handles common development issues:
- Domain mismatches on localhost
- Invalid tokens during testing
- API configuration errors

All development errors are logged but allowed to pass for testing purposes.
